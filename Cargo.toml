[package]
name = "arien"
version = "0.1.0"
edition = "2024"
authors = ["Arien AI Team"]
description = "A production-ready, fully agentic AI-powered CLI tool system"
license = "MIT OR Apache-2.0"
repository = "https://github.com/arien-ai/arien"
keywords = ["ai", "cli", "agent", "llm", "automation"]
categories = ["command-line-utilities", "development-tools"]
rust-version = "1.85.0"

[[bin]]
name = "arien"
path = "src/main.rs"

[dependencies]
# CLI and Terminal UI
clap = { version = "4.5", features = ["derive", "env", "color"] }
ratatui = { version = "0.29", features = ["all-widgets", "crossterm"] }
crossterm = { version = "0.28", features = ["event-stream"] }
console = "0.15"
indicatif = "0.17"

# Async Runtime and Networking
tokio = { version = "1.40", features = ["full", "io-util"] }
reqwest = { version = "0.12", features = ["json", "stream", "rustls-tls"], default-features = false }
futures = "0.3"
async-stream = "0.3"
async-trait = "0.1"

# Serialization and Schema
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
schemars = { version = "0.8", features = ["derive"] }
jsonschema = "0.18"

# Error Handling and Logging
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# File System and Path Operations
walkdir = "2.5"
glob = "0.3"
tempfile = "3.12"
dirs = "5.0"

# Security and Encryption
keyring = "3.6"
ring = "0.17"
base64 = "0.22"

# Configuration and Storage
toml = "0.8"
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
uuid = { version = "1.10", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Text Processing and Regex
regex = "1.10"
aho-corasick = "1.1"
similar = "2.6"

# System Integration
which = "6.0"
shell-words = "1.1"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.12"
assert_cmd = "2.0"
predicates = "3.1"
wiremock = "0.6"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0

[workspace]
members = ["."]
