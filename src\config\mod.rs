use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use tokio::fs;
use tracing::{debug, info, warn};

/// LLM Provider configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LlmProvider {
    OpenAI {
        api_key: String,
        model: String,
        base_url: Option<String>,
    },
    Deepseek {
        api_key: String,
        model: String,
        base_url: Option<String>,
    },
    Ollama {
        base_url: String,
        model: String,
    },
}

impl LlmProvider {
    pub fn name(&self) -> &'static str {
        match self {
            LlmProvider::OpenAI { .. } => "OpenAI",
            LlmProvider::Deepseek { .. } => "Deepseek",
            LlmProvider::Ollama { .. } => "Ollama",
        }
    }

    pub fn model(&self) -> &str {
        match self {
            LlmProvider::OpenAI { model, .. } => model,
            LlmProvider::Deepseek { model, .. } => model,
            LlmProvider::Ollama { model, .. } => model,
        }
    }

    pub fn requires_api_key(&self) -> bool {
        matches!(self, LlmProvider::OpenAI { .. } | LlmProvider::Deepseek { .. })
    }
}

/// Safety configuration for command execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyConfig {
    /// Require confirmation for destructive operations
    pub require_confirmation: bool,
    /// List of commands that always require confirmation
    pub dangerous_commands: Vec<String>,
    /// Maximum file size for operations (in bytes)
    pub max_file_size: u64,
    /// Timeout for shell commands (in seconds)
    pub command_timeout: u64,
}

impl Default for SafetyConfig {
    fn default() -> Self {
        Self {
            require_confirmation: true,
            dangerous_commands: vec![
                "rm".to_string(),
                "del".to_string(),
                "rmdir".to_string(),
                "format".to_string(),
                "mkfs".to_string(),
                "dd".to_string(),
                "sudo".to_string(),
            ],
            max_file_size: 100 * 1024 * 1024, // 100MB
            command_timeout: 300, // 5 minutes
        }
    }
}

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// LLM provider configuration
    pub llm: LlmProvider,
    /// Safety settings
    pub safety: SafetyConfig,
    /// Enable streaming responses
    pub streaming: bool,
    /// Maximum conversation history length
    pub max_history: usize,
    /// Auto-save conversations
    pub auto_save: bool,
    /// Default working directory
    pub working_directory: Option<PathBuf>,
    /// Custom system prompt additions
    pub custom_system_prompt: Option<String>,
}

impl Config {
    pub fn new(llm: LlmProvider) -> Self {
        Self {
            llm,
            safety: SafetyConfig::default(),
            streaming: true,
            max_history: 100,
            auto_save: true,
            working_directory: None,
            custom_system_prompt: None,
        }
    }
}

/// Configuration manager handles loading, saving, and managing configuration
pub struct ConfigManager {
    config_path: PathBuf,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub async fn new(custom_path: Option<&Path>) -> Result<Self> {
        let config_path = match custom_path {
            Some(path) => path.to_path_buf(),
            None => Self::default_config_path()?,
        };

        // Ensure config directory exists
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent).await
                .with_context(|| format!("Failed to create config directory: {}", parent.display()))?;
        }

        Ok(Self { config_path })
    }

    /// Get the default configuration file path
    fn default_config_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .context("Failed to get user config directory")?;
        
        Ok(config_dir.join("arien").join("config.toml"))
    }

    /// Load configuration from file
    pub async fn load_config(&self) -> Result<Config> {
        debug!("Loading configuration from: {}", self.config_path.display());
        
        let content = fs::read_to_string(&self.config_path).await
            .with_context(|| format!("Failed to read config file: {}", self.config_path.display()))?;
        
        let config: Config = toml::from_str(&content)
            .with_context(|| format!("Failed to parse config file: {}", self.config_path.display()))?;
        
        info!("Configuration loaded successfully");
        Ok(config)
    }

    /// Save configuration to file
    pub async fn save_config(&self, config: &Config) -> Result<()> {
        debug!("Saving configuration to: {}", self.config_path.display());
        
        let content = toml::to_string_pretty(config)
            .context("Failed to serialize configuration")?;
        
        fs::write(&self.config_path, content).await
            .with_context(|| format!("Failed to write config file: {}", self.config_path.display()))?;
        
        info!("Configuration saved successfully");
        Ok(())
    }

    /// Check if configuration file exists
    pub async fn config_exists(&self) -> bool {
        self.config_path.exists()
    }

    /// Reset configuration by removing the file
    pub async fn reset_config(&self) -> Result<()> {
        if self.config_path.exists() {
            fs::remove_file(&self.config_path).await
                .with_context(|| format!("Failed to remove config file: {}", self.config_path.display()))?;
            info!("Configuration file removed");
        } else {
            warn!("Configuration file does not exist");
        }
        Ok(())
    }

    /// Get the configuration file path
    pub fn config_path(&self) -> &Path {
        &self.config_path
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_config_manager() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let config_path = temp_dir.path().join("test_config.toml");
        
        let manager = ConfigManager::new(Some(&config_path)).await?;
        
        // Test saving and loading
        let original_config = Config::new(LlmProvider::OpenAI {
            api_key: "test-key".to_string(),
            model: "gpt-4".to_string(),
            base_url: None,
        });
        
        manager.save_config(&original_config).await?;
        let loaded_config = manager.load_config().await?;
        
        assert_eq!(original_config.llm, loaded_config.llm);
        assert_eq!(original_config.streaming, loaded_config.streaming);
        
        Ok(())
    }

    #[test]
    fn test_llm_provider_methods() {
        let openai = LlmProvider::OpenAI {
            api_key: "key".to_string(),
            model: "gpt-4".to_string(),
            base_url: None,
        };
        
        assert_eq!(openai.name(), "OpenAI");
        assert_eq!(openai.model(), "gpt-4");
        assert!(openai.requires_api_key());
        
        let ollama = LlmProvider::Ollama {
            base_url: "http://localhost:11434".to_string(),
            model: "llama2".to_string(),
        };
        
        assert_eq!(ollama.name(), "Ollama");
        assert_eq!(ollama.model(), "llama2");
        assert!(!ollama.requires_api_key());
    }
}
