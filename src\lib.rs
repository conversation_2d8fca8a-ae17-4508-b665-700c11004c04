//! # Arien AI
//!
//! A production-ready, fully agentic AI-powered CLI tool system built with Rust 2024 edition.
//!
//! ## Features
//!
//! - **Multi-LLM Support**: OpenAI, Deepseek, and Ollama integration
//! - **Type-Safe Tool System**: JSON schema validation with compile-time safety
//! - **Secure Shell Integration**: Cross-platform command execution with safety checks
//! - **Rich Terminal UI**: Interactive setup and real-time streaming responses
//! - **Context-Aware**: Intelligent project discovery and context management
//! - **Production Ready**: Comprehensive error handling, logging, and security

pub mod agent;
pub mod config;
pub mod llm;
pub mod shell;
pub mod tools;
pub mod ui;

// Re-export commonly used types
pub use agent::{AgentOrchestrator, AgentResult};
pub use config::{Config, ConfigManager, LlmProvider};
pub use tools::{Tool, ToolRegistry, ToolResult};

/// Result type alias for the entire crate
pub type Result<T> = anyhow::Result<T>;

/// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Build information
pub const BUILD_INFO: &str = concat!(
    "Arien AI v",
    env!("CARGO_PKG_VERSION"),
    " (Rust ",
    env!("RUSTC_VERSION"),
    ")"
);

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(BUILD_INFO.contains(VERSION));
    }
}
