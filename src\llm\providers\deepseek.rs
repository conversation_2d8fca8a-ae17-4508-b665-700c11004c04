use anyhow::{Context, Result};
use async_trait::async_trait;
use futures::{Stream, StreamExt};
use reqwest::header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE};
use serde::{Deserialize, Serialize};
use std::pin::Pin;
use tracing::{debug, warn};

use crate::llm::{
    LlmProvider, LlmRequest, LlmResponse, ResponseChunk, TokenUsage,
    conversation::{Message, MessageRole}
};

use super::{create_http_client, handle_http_error};

/// Deepseek API provider
#[derive(Debug)]
pub struct DeepseekProvider {
    client: reqwest::Client,
    api_key: String,
    base_url: String,
}

impl DeepseekProvider {
    /// Create a new Deepseek provider
    pub fn new(api_key: String, base_url: Option<String>) -> Result<Self> {
        let client = create_http_client()?;
        let base_url = base_url.unwrap_or_else(|| "https://api.deepseek.com/v1".to_string());
        
        Ok(Self {
            client,
            api_key,
            base_url,
        })
    }

    /// Create headers for API requests
    fn create_headers(&self) -> Result<HeaderMap> {
        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", self.api_key))
                .context("Invalid API key format")?,
        );
        Ok(headers)
    }

    /// Convert internal message to Deepseek format (same as OpenAI)
    fn convert_message(&self, message: &Message) -> DeepseekMessage {
        let role = match message.role {
            MessageRole::System => "system".to_string(),
            MessageRole::User => "user".to_string(),
            MessageRole::Assistant => "assistant".to_string(),
            MessageRole::Tool => "tool".to_string(),
        };

        DeepseekMessage {
            role,
            content: message.content.clone(),
        }
    }
}

#[async_trait]
impl LlmProvider for DeepseekProvider {
    fn name(&self) -> &str {
        "Deepseek"
    }

    async fn get_models(&self) -> Result<Vec<String>> {
        // Deepseek has a limited set of known models
        Ok(vec![
            "deepseek-chat".to_string(),
            "deepseek-reasoner".to_string(),
            "deepseek-coder".to_string(),
        ])
    }

    async fn generate(&self, request: LlmRequest) -> Result<LlmResponse> {
        let url = format!("{}/chat/completions", self.base_url);
        let headers = self.create_headers()?;

        let mut messages: Vec<DeepseekMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            messages.insert(0, DeepseekMessage {
                role: "system".to_string(),
                content: system_prompt,
            });
        }

        let deepseek_request = DeepseekRequest {
            model: request.model.clone(),
            messages,
            max_tokens: request.max_tokens,
            temperature: request.temperature,
            top_p: request.top_p,
            stream: false,
        };

        debug!("Sending Deepseek request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&deepseek_request)
            .send()
            .await
            .context("Failed to send request")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let deepseek_response: DeepseekResponse = response
            .json()
            .await
            .context("Failed to parse response")?;

        let choice = deepseek_response.choices.into_iter().next()
            .context("No choices in response")?;

        Ok(LlmResponse {
            content: choice.message.content,
            token_usage: deepseek_response.usage.map(|usage| TokenUsage {
                prompt_tokens: usage.prompt_tokens,
                completion_tokens: usage.completion_tokens,
                total_tokens: usage.total_tokens,
            }),
            tool_calls: None, // Deepseek doesn't support function calling yet
            model: request.model,
            finish_reason: choice.finish_reason,
        })
    }

    async fn generate_stream<'a>(
        &'a self,
        request: LlmRequest,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send + 'a>>> {
        let url = format!("{}/chat/completions", self.base_url);
        let headers = self.create_headers()?;

        let mut messages: Vec<DeepseekMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            messages.insert(0, DeepseekMessage {
                role: "system".to_string(),
                content: system_prompt,
            });
        }

        let deepseek_request = DeepseekRequest {
            model: request.model.clone(),
            messages,
            max_tokens: request.max_tokens,
            temperature: request.temperature,
            top_p: request.top_p,
            stream: true,
        };

        debug!("Sending streaming Deepseek request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&deepseek_request)
            .send()
            .await
            .context("Failed to send streaming request")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let stream = response.bytes_stream().map(move |chunk_result| {
            match chunk_result {
                Ok(chunk) => {
                    let chunk_str = String::from_utf8_lossy(&chunk);
                    self.parse_sse_chunk(&chunk_str)
                }
                Err(e) => Err(anyhow::anyhow!("Stream error: {}", e)),
            }
        });

        Ok(Box::pin(stream))
    }

    fn supports_function_calling(&self) -> bool {
        false // Deepseek doesn't support function calling yet
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    fn get_context_length(&self, model: &str) -> Option<u32> {
        match model {
            "deepseek-chat" => Some(32768),
            "deepseek-reasoner" => Some(16384),
            "deepseek-coder" => Some(4096),
            _ => None,
        }
    }
}

impl DeepseekProvider {
    /// Parse Server-Sent Events chunk
    fn parse_sse_chunk(&self, chunk: &str) -> Result<ResponseChunk> {
        for line in chunk.lines() {
            if line.starts_with("data: ") {
                let data = &line[6..];
                if data == "[DONE]" {
                    return Ok(ResponseChunk {
                        content: String::new(),
                        is_final: true,
                        token_usage: None,
                        tool_calls: None,
                    });
                }

                let streaming_response: DeepseekStreamingResponse = serde_json::from_str(data)
                    .context("Failed to parse streaming response")?;

                if let Some(choice) = streaming_response.choices.into_iter().next() {
                    let content = choice.delta.content.unwrap_or_default();
                    let is_final = choice.finish_reason.is_some();

                    return Ok(ResponseChunk {
                        content,
                        is_final,
                        token_usage: streaming_response.usage.map(|usage| TokenUsage {
                            prompt_tokens: usage.prompt_tokens,
                            completion_tokens: usage.completion_tokens,
                            total_tokens: usage.total_tokens,
                        }),
                        tool_calls: None,
                    });
                }
            }
        }

        Ok(ResponseChunk {
            content: String::new(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        })
    }
}

// Deepseek API types (similar to OpenAI but simplified)
#[derive(Debug, Serialize, Deserialize)]
struct DeepseekRequest {
    model: String,
    messages: Vec<DeepseekMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    max_tokens: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    top_p: Option<f32>,
    stream: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct DeepseekMessage {
    role: String,
    content: String,
}

#[derive(Debug, Deserialize)]
struct DeepseekResponse {
    choices: Vec<DeepseekChoice>,
    usage: Option<DeepseekUsage>,
}

#[derive(Debug, Deserialize)]
struct DeepseekChoice {
    message: DeepseekMessage,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct DeepseekUsage {
    prompt_tokens: u32,
    completion_tokens: u32,
    total_tokens: u32,
}

#[derive(Debug, Deserialize)]
struct DeepseekStreamingResponse {
    choices: Vec<DeepseekStreamingChoice>,
    usage: Option<DeepseekUsage>,
}

#[derive(Debug, Deserialize)]
struct DeepseekStreamingChoice {
    delta: DeepseekDelta,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct DeepseekDelta {
    #[serde(skip_serializing_if = "Option::is_none")]
    content: Option<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deepseek_provider_creation() {
        let provider = DeepseekProvider::new(
            "test-key".to_string(),
            Some("https://api.deepseek.com/v1".to_string()),
        );
        assert!(provider.is_ok());
    }

    #[test]
    fn test_context_length() {
        let provider = DeepseekProvider::new("test".to_string(), None).unwrap();
        assert_eq!(provider.get_context_length("deepseek-chat"), Some(32768));
        assert_eq!(provider.get_context_length("deepseek-reasoner"), Some(16384));
        assert_eq!(provider.get_context_length("unknown-model"), None);
    }

    #[test]
    fn test_supports_features() {
        let provider = DeepseekProvider::new("test".to_string(), None).unwrap();
        assert!(!provider.supports_function_calling()); // Deepseek doesn't support this yet
        assert!(provider.supports_streaming());
        assert_eq!(provider.name(), "Deepseek");
    }

    #[tokio::test]
    async fn test_get_models() {
        let provider = DeepseekProvider::new("test".to_string(), None).unwrap();
        let models = provider.get_models().await.unwrap();
        assert!(models.contains(&"deepseek-chat".to_string()));
        assert!(models.contains(&"deepseek-reasoner".to_string()));
    }
}
