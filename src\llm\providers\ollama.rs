use anyhow::{Context, Result};
use async_trait::async_trait;
use futures::{Stream, StreamExt};
use reqwest::header::{HeaderMap, HeaderValue, CONTENT_TYPE};
use serde::{Deserialize, Serialize};
use std::pin::Pin;
use tracing::{debug, warn};

use crate::llm::{
    LlmProvider, LlmRequest, LlmResponse, ResponseChunk, TokenUsage,
    conversation::{Message, MessageRole}
};

use super::{create_http_client, handle_http_error};

/// Ollama local API provider
#[derive(Debug)]
pub struct OllamaProvider {
    client: reqwest::Client,
    base_url: String,
}

impl OllamaProvider {
    /// Create a new Ollama provider
    pub fn new(base_url: String) -> Result<Self> {
        let client = create_http_client()?;
        
        Ok(Self {
            client,
            base_url,
        })
    }

    /// Create headers for API requests
    fn create_headers(&self) -> HeaderMap {
        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers
    }

    /// Convert internal message to Ollama format
    fn convert_message(&self, message: &Message) -> OllamaMessage {
        let role = match message.role {
            MessageRole::System => "system".to_string(),
            MessageRole::User => "user".to_string(),
            MessageRole::Assistant => "assistant".to_string(),
            MessageRole::Tool => "user".to_string(), // Ollama doesn't have tool role
        };

        OllamaMessage {
            role,
            content: message.content.clone(),
        }
    }

    /// Check if Ollama is available
    pub async fn check_availability(&self) -> Result<bool> {
        let url = format!("{}/api/tags", self.base_url);
        let headers = self.create_headers();

        match self.client
            .get(&url)
            .headers(headers)
            .timeout(std::time::Duration::from_secs(5))
            .send()
            .await
        {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }
}

#[async_trait]
impl LlmProvider for OllamaProvider {
    fn name(&self) -> &str {
        "Ollama"
    }

    async fn get_models(&self) -> Result<Vec<String>> {
        let url = format!("{}/api/tags", self.base_url);
        let headers = self.create_headers();

        debug!("Fetching Ollama models from: {}", url);

        let response = self.client
            .get(&url)
            .headers(headers)
            .send()
            .await
            .context("Failed to fetch models from Ollama")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let models_response: OllamaModelsResponse = response
            .json()
            .await
            .context("Failed to parse models response")?;

        Ok(models_response.models.into_iter().map(|model| model.name).collect())
    }

    async fn generate(&self, request: LlmRequest) -> Result<LlmResponse> {
        let url = format!("{}/api/chat", self.base_url);
        let headers = self.create_headers();

        let mut messages: Vec<OllamaMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            messages.insert(0, OllamaMessage {
                role: "system".to_string(),
                content: system_prompt,
            });
        }

        let ollama_request = OllamaRequest {
            model: request.model.clone(),
            messages,
            stream: false,
            options: Some(OllamaOptions {
                temperature: request.temperature,
                top_p: request.top_p,
                num_predict: request.max_tokens.map(|t| t as i32),
            }),
        };

        debug!("Sending Ollama request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&ollama_request)
            .send()
            .await
            .context("Failed to send request to Ollama")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let ollama_response: OllamaResponse = response
            .json()
            .await
            .context("Failed to parse Ollama response")?;

        // Estimate token usage since Ollama doesn't provide it
        let prompt_tokens = self.estimate_tokens(&request.messages.iter()
            .map(|m| m.content.as_str())
            .collect::<Vec<_>>()
            .join(" "));
        let completion_tokens = self.estimate_tokens(&ollama_response.message.content);

        Ok(LlmResponse {
            content: ollama_response.message.content,
            token_usage: Some(TokenUsage {
                prompt_tokens,
                completion_tokens,
                total_tokens: prompt_tokens + completion_tokens,
            }),
            tool_calls: None, // Ollama doesn't support function calling
            model: request.model,
            finish_reason: if ollama_response.done { Some("stop".to_string()) } else { None },
        })
    }

    async fn generate_stream<'a>(
        &'a self,
        request: LlmRequest,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send + 'a>>> {
        let url = format!("{}/api/chat", self.base_url);
        let headers = self.create_headers();

        let mut messages: Vec<OllamaMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            messages.insert(0, OllamaMessage {
                role: "system".to_string(),
                content: system_prompt,
            });
        }

        let ollama_request = OllamaRequest {
            model: request.model.clone(),
            messages,
            stream: true,
            options: Some(OllamaOptions {
                temperature: request.temperature,
                top_p: request.top_p,
                num_predict: request.max_tokens.map(|t| t as i32),
            }),
        };

        debug!("Sending streaming Ollama request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&ollama_request)
            .send()
            .await
            .context("Failed to send streaming request to Ollama")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let stream = response.bytes_stream().map(move |chunk_result| {
            match chunk_result {
                Ok(chunk) => {
                    let chunk_str = String::from_utf8_lossy(&chunk);
                    self.parse_streaming_chunk(&chunk_str)
                }
                Err(e) => Err(anyhow::anyhow!("Stream error: {}", e)),
            }
        });

        Ok(Box::pin(stream))
    }

    fn supports_function_calling(&self) -> bool {
        false // Ollama doesn't support function calling
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    fn get_context_length(&self, _model: &str) -> Option<u32> {
        // Ollama context length varies by model, default to a conservative estimate
        Some(4096)
    }
}

impl OllamaProvider {
    /// Parse streaming chunk from Ollama
    fn parse_streaming_chunk(&self, chunk: &str) -> Result<ResponseChunk> {
        for line in chunk.lines() {
            if line.trim().is_empty() {
                continue;
            }

            let streaming_response: OllamaStreamingResponse = serde_json::from_str(line)
                .context("Failed to parse Ollama streaming response")?;

            let content = streaming_response.message.content;
            let is_final = streaming_response.done;

            // Estimate token usage for final chunk
            let token_usage = if is_final {
                let completion_tokens = self.estimate_tokens(&content);
                Some(TokenUsage {
                    prompt_tokens: 0, // We don't have this info in streaming
                    completion_tokens,
                    total_tokens: completion_tokens,
                })
            } else {
                None
            };

            return Ok(ResponseChunk {
                content,
                is_final,
                token_usage,
                tool_calls: None,
            });
        }

        Ok(ResponseChunk {
            content: String::new(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        })
    }
}

// Ollama API types
#[derive(Debug, Serialize, Deserialize)]
struct OllamaRequest {
    model: String,
    messages: Vec<OllamaMessage>,
    stream: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    options: Option<OllamaOptions>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OllamaMessage {
    role: String,
    content: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct OllamaOptions {
    #[serde(skip_serializing_if = "Option::is_none")]
    temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    top_p: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    num_predict: Option<i32>,
}

#[derive(Debug, Deserialize)]
struct OllamaResponse {
    message: OllamaMessage,
    done: bool,
}

#[derive(Debug, Deserialize)]
struct OllamaStreamingResponse {
    message: OllamaMessage,
    done: bool,
}

#[derive(Debug, Deserialize)]
struct OllamaModelsResponse {
    models: Vec<OllamaModel>,
}

#[derive(Debug, Deserialize)]
struct OllamaModel {
    name: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ollama_provider_creation() {
        let provider = OllamaProvider::new("http://localhost:11434".to_string());
        assert!(provider.is_ok());
    }

    #[test]
    fn test_supports_features() {
        let provider = OllamaProvider::new("http://localhost:11434".to_string()).unwrap();
        assert!(!provider.supports_function_calling()); // Ollama doesn't support this
        assert!(provider.supports_streaming());
        assert_eq!(provider.name(), "Ollama");
    }

    #[test]
    fn test_context_length() {
        let provider = OllamaProvider::new("http://localhost:11434".to_string()).unwrap();
        assert_eq!(provider.get_context_length("any-model"), Some(4096));
    }

    #[test]
    fn test_message_conversion() {
        let provider = OllamaProvider::new("http://localhost:11434".to_string()).unwrap();
        let message = Message::user("Hello");
        let ollama_msg = provider.convert_message(&message);
        
        assert_eq!(ollama_msg.role, "user");
        assert_eq!(ollama_msg.content, "Hello");
    }
}
