use anyhow::{Context, Result};
use async_trait::async_trait;
use futures::{Stream, StreamExt};
use reqwest::header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::pin::Pin;
use tracing::{debug, warn};

use crate::llm::{
    LlmProvider, LlmRequest, LlmResponse, ResponseChunk, TokenUsage,
    conversation::{Message, MessageRole}
};

use super::{create_http_client, handle_http_error};

/// OpenAI API provider
#[derive(Debug)]
pub struct OpenAiProvider {
    client: reqwest::Client,
    api_key: String,
    base_url: String,
}

impl OpenAiProvider {
    /// Create a new OpenAI provider
    pub fn new(api_key: String, base_url: Option<String>) -> Result<Self> {
        let client = create_http_client()?;
        let base_url = base_url.unwrap_or_else(|| "https://api.openai.com/v1".to_string());
        
        Ok(Self {
            client,
            api_key,
            base_url,
        })
    }

    /// Create headers for API requests
    fn create_headers(&self) -> Result<HeaderMap> {
        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", self.api_key))
                .context("Invalid API key format")?,
        );
        Ok(headers)
    }

    /// Convert internal message to OpenAI format
    fn convert_message(&self, message: &Message) -> OpenAiMessage {
        let role = match message.role {
            MessageRole::System => "system".to_string(),
            MessageRole::User => "user".to_string(),
            MessageRole::Assistant => "assistant".to_string(),
            MessageRole::Tool => "tool".to_string(),
        };

        OpenAiMessage {
            role,
            content: Some(message.content.clone()),
            tool_calls: message.tool_calls.as_ref().map(|calls| {
                calls.iter().map(|call| OpenAiToolCall {
                    id: call.id.clone().unwrap_or_else(|| uuid::Uuid::new_v4().to_string()),
                    r#type: "function".to_string(),
                    function: OpenAiFunction {
                        name: call.name.clone(),
                        arguments: serde_json::to_string(&call.parameters).unwrap_or_default(),
                    },
                }).collect()
            }),
            tool_call_id: None,
        }
    }

    /// Convert tool schemas to OpenAI function format
    fn convert_tools(&self, tools: &[crate::tools::ToolSchema]) -> Vec<OpenAiTool> {
        tools.iter().map(|tool| OpenAiTool {
            r#type: "function".to_string(),
            function: OpenAiToolFunction {
                name: tool.name.clone(),
                description: tool.description.clone(),
                parameters: serde_json::to_value(&tool.parameters).unwrap_or_default(),
            },
        }).collect()
    }
}

#[async_trait]
impl LlmProvider for OpenAiProvider {
    fn name(&self) -> &str {
        "OpenAI"
    }

    async fn get_models(&self) -> Result<Vec<String>> {
        let url = format!("{}/models", self.base_url);
        let headers = self.create_headers()?;

        debug!("Fetching OpenAI models from: {}", url);

        let response = self.client
            .get(&url)
            .headers(headers)
            .send()
            .await
            .context("Failed to fetch models")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let models_response: OpenAiModelsResponse = response
            .json()
            .await
            .context("Failed to parse models response")?;

        Ok(models_response.data.into_iter().map(|model| model.id).collect())
    }

    async fn generate(&self, request: LlmRequest) -> Result<LlmResponse> {
        let url = format!("{}/chat/completions", self.base_url);
        let headers = self.create_headers()?;

        let messages: Vec<OpenAiMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        let mut openai_request = OpenAiRequest {
            model: request.model.clone(),
            messages,
            max_tokens: request.max_tokens,
            temperature: request.temperature,
            top_p: request.top_p,
            stream: false,
            tools: request.tools.as_ref().map(|tools| self.convert_tools(tools)),
            tool_choice: if request.tools.is_some() { Some("auto".to_string()) } else { None },
        };

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            openai_request.messages.insert(0, OpenAiMessage {
                role: "system".to_string(),
                content: Some(system_prompt),
                tool_calls: None,
                tool_call_id: None,
            });
        }

        debug!("Sending OpenAI request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&openai_request)
            .send()
            .await
            .context("Failed to send request")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let openai_response: OpenAiResponse = response
            .json()
            .await
            .context("Failed to parse response")?;

        let choice = openai_response.choices.into_iter().next()
            .context("No choices in response")?;

        let tool_calls = choice.message.tool_calls.map(|calls| {
            calls.into_iter().map(|call| crate::tools::ToolCall {
                name: call.function.name,
                parameters: serde_json::from_str(&call.function.arguments)
                    .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new())),
                id: Some(call.id),
            }).collect()
        });

        Ok(LlmResponse {
            content: choice.message.content.unwrap_or_default(),
            token_usage: openai_response.usage.map(|usage| TokenUsage {
                prompt_tokens: usage.prompt_tokens,
                completion_tokens: usage.completion_tokens,
                total_tokens: usage.total_tokens,
            }),
            tool_calls,
            model: request.model,
            finish_reason: choice.finish_reason,
        })
    }

    async fn generate_stream<'a>(
        &'a self,
        request: LlmRequest,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send + 'a>>> {
        let url = format!("{}/chat/completions", self.base_url);
        let headers = self.create_headers()?;

        let messages: Vec<OpenAiMessage> = request.messages.iter()
            .map(|msg| self.convert_message(msg))
            .collect();

        let mut openai_request = OpenAiRequest {
            model: request.model.clone(),
            messages,
            max_tokens: request.max_tokens,
            temperature: request.temperature,
            top_p: request.top_p,
            stream: true,
            tools: request.tools.as_ref().map(|tools| self.convert_tools(tools)),
            tool_choice: if request.tools.is_some() { Some("auto".to_string()) } else { None },
        };

        // Add system prompt as first message if provided
        if let Some(system_prompt) = request.system_prompt {
            openai_request.messages.insert(0, OpenAiMessage {
                role: "system".to_string(),
                content: Some(system_prompt),
                tool_calls: None,
                tool_call_id: None,
            });
        }

        debug!("Sending streaming OpenAI request to: {}", url);

        let response = self.client
            .post(&url)
            .headers(headers)
            .json(&openai_request)
            .send()
            .await
            .context("Failed to send streaming request")?;

        if !response.status().is_success() {
            return Err(handle_http_error(response).await);
        }

        let stream = response.bytes_stream().map(move |chunk_result| {
            match chunk_result {
                Ok(chunk) => {
                    let chunk_str = String::from_utf8_lossy(&chunk);
                    self.parse_sse_chunk(&chunk_str)
                }
                Err(e) => Err(anyhow::anyhow!("Stream error: {}", e)),
            }
        });

        Ok(Box::pin(stream))
    }

    fn supports_function_calling(&self) -> bool {
        true
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    fn get_context_length(&self, model: &str) -> Option<u32> {
        match model {
            "gpt-4" | "gpt-4-0613" => Some(8192),
            "gpt-4-32k" | "gpt-4-32k-0613" => Some(32768),
            "gpt-4-turbo" | "gpt-4-turbo-preview" => Some(128000),
            "gpt-4o" | "gpt-4o-mini" => Some(128000),
            "gpt-3.5-turbo" | "gpt-3.5-turbo-0613" => Some(4096),
            "gpt-3.5-turbo-16k" => Some(16384),
            _ => None,
        }
    }
}

impl OpenAiProvider {
    /// Parse Server-Sent Events chunk
    fn parse_sse_chunk(&self, chunk: &str) -> Result<ResponseChunk> {
        for line in chunk.lines() {
            if line.starts_with("data: ") {
                let data = &line[6..];
                if data == "[DONE]" {
                    return Ok(ResponseChunk {
                        content: String::new(),
                        is_final: true,
                        token_usage: None,
                        tool_calls: None,
                    });
                }

                let streaming_response: OpenAiStreamingResponse = serde_json::from_str(data)
                    .context("Failed to parse streaming response")?;

                if let Some(choice) = streaming_response.choices.into_iter().next() {
                    let content = choice.delta.content.unwrap_or_default();
                    let is_final = choice.finish_reason.is_some();

                    let tool_calls = choice.delta.tool_calls.map(|calls| {
                        calls.into_iter().filter_map(|call| {
                            if let Some(function) = call.function {
                                Some(crate::tools::ToolCall {
                                    name: function.name,
                                    parameters: serde_json::from_str(&function.arguments)
                                        .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new())),
                                    id: call.id,
                                })
                            } else {
                                None
                            }
                        }).collect()
                    });

                    return Ok(ResponseChunk {
                        content,
                        is_final,
                        token_usage: streaming_response.usage.map(|usage| TokenUsage {
                            prompt_tokens: usage.prompt_tokens,
                            completion_tokens: usage.completion_tokens,
                            total_tokens: usage.total_tokens,
                        }),
                        tool_calls,
                    });
                }
            }
        }

        Ok(ResponseChunk {
            content: String::new(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        })
    }
}

// OpenAI API types
#[derive(Debug, Serialize, Deserialize)]
struct OpenAiRequest {
    model: String,
    messages: Vec<OpenAiMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    max_tokens: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    top_p: Option<f32>,
    stream: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    tools: Option<Vec<OpenAiTool>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_choice: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAiMessage {
    role: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    content: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_calls: Option<Vec<OpenAiToolCall>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_call_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAiToolCall {
    id: String,
    r#type: String,
    function: OpenAiFunction,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAiFunction {
    name: String,
    arguments: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAiTool {
    r#type: String,
    function: OpenAiToolFunction,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAiToolFunction {
    name: String,
    description: String,
    parameters: serde_json::Value,
}

#[derive(Debug, Deserialize)]
struct OpenAiResponse {
    choices: Vec<OpenAiChoice>,
    usage: Option<OpenAiUsage>,
}

#[derive(Debug, Deserialize)]
struct OpenAiChoice {
    message: OpenAiMessage,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct OpenAiUsage {
    prompt_tokens: u32,
    completion_tokens: u32,
    total_tokens: u32,
}

#[derive(Debug, Deserialize)]
struct OpenAiStreamingResponse {
    choices: Vec<OpenAiStreamingChoice>,
    usage: Option<OpenAiUsage>,
}

#[derive(Debug, Deserialize)]
struct OpenAiStreamingChoice {
    delta: OpenAiDelta,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct OpenAiDelta {
    #[serde(skip_serializing_if = "Option::is_none")]
    content: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_calls: Option<Vec<OpenAiDeltaToolCall>>,
}

#[derive(Debug, Deserialize)]
struct OpenAiDeltaToolCall {
    #[serde(skip_serializing_if = "Option::is_none")]
    id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    function: Option<OpenAiFunction>,
}

#[derive(Debug, Deserialize)]
struct OpenAiModelsResponse {
    data: Vec<OpenAiModel>,
}

#[derive(Debug, Deserialize)]
struct OpenAiModel {
    id: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_openai_provider_creation() {
        let provider = OpenAiProvider::new(
            "test-key".to_string(),
            Some("https://api.openai.com/v1".to_string()),
        );
        assert!(provider.is_ok());
    }

    #[test]
    fn test_context_length() {
        let provider = OpenAiProvider::new("test".to_string(), None).unwrap();
        assert_eq!(provider.get_context_length("gpt-4"), Some(8192));
        assert_eq!(provider.get_context_length("gpt-4-32k"), Some(32768));
        assert_eq!(provider.get_context_length("unknown-model"), None);
    }

    #[test]
    fn test_supports_features() {
        let provider = OpenAiProvider::new("test".to_string(), None).unwrap();
        assert!(provider.supports_function_calling());
        assert!(provider.supports_streaming());
        assert_eq!(provider.name(), "OpenAI");
    }
}
