use anyhow::Result;
use clap::{Parser, Subcommand};
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use arien::{
    agent::AgentOrchestrator,
    config::{Config, ConfigManager},
    ui::{InteractiveMode, SetupWizard},
};

#[derive(Parser)]
#[command(name = "arien")]
#[command(about = "Arien AI - A production-ready, fully agentic AI-powered CLI tool system")]
#[command(version = env!("CARGO_PKG_VERSION"))]
#[command(author = "Arien AI Team")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,

    /// Enable verbose logging
    #[arg(short, long, global = true)]
    verbose: bool,

    /// Configuration file path
    #[arg(short, long, global = true)]
    config: Option<std::path::PathBuf>,
}

#[derive(Subcommand)]
enum Commands {
    /// Run interactive mode
    Interactive,
    /// Setup or reconfigure Arien AI
    Setup,
    /// Execute a single command
    Execute {
        /// The command to execute
        #[arg(required = true)]
        command: Vec<String>,
    },
    /// Show current configuration
    Config,
    /// Reset configuration to defaults
    Reset,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    init_logging(cli.verbose)?;

    info!("Starting Arien AI v{}", env!("CARGO_PKG_VERSION"));

    // Initialize configuration manager
    let config_manager = ConfigManager::new(cli.config.as_deref()).await?;

    match cli.command {
        Some(Commands::Setup) => {
            info!("Starting setup wizard");
            let mut wizard = SetupWizard::new();
            let config = wizard.run().await?;
            config_manager.save_config(&config).await?;
            println!("✅ Configuration saved successfully!");
        }
        Some(Commands::Interactive) => {
            let config = ensure_configured(&config_manager).await?;
            info!("Starting interactive mode");
            let mut interactive = InteractiveMode::new(config).await?;
            interactive.run().await?;
        }
        Some(Commands::Execute { command }) => {
            let config = ensure_configured(&config_manager).await?;
            info!("Executing single command: {:?}", command);
            let orchestrator = AgentOrchestrator::new(config).await?;
            let command_str = command.join(" ");
            orchestrator.execute_command(&command_str).await?;
        }
        Some(Commands::Config) => {
            match config_manager.load_config().await {
                Ok(config) => {
                    println!("Current configuration:");
                    println!("{}", toml::to_string_pretty(&config)?);
                }
                Err(_) => {
                    println!("No configuration found. Run 'arien setup' to configure.");
                }
            }
        }
        Some(Commands::Reset) => {
            config_manager.reset_config().await?;
            println!("✅ Configuration reset successfully!");
        }
        None => {
            // No subcommand provided - check if configured, if not run setup, then interactive
            match config_manager.load_config().await {
                Ok(config) => {
                    println!("Welcome back to Arien AI! 🚀");
                    println!("Continue with saved configuration? (y/n)");
                    
                    let mut input = String::new();
                    std::io::stdin().read_line(&mut input)?;
                    
                    if input.trim().to_lowercase().starts_with('y') {
                        info!("Starting interactive mode with saved config");
                        let mut interactive = InteractiveMode::new(config).await?;
                        interactive.run().await?;
                    } else {
                        info!("Starting setup wizard");
                        let mut wizard = SetupWizard::new();
                        let new_config = wizard.run().await?;
                        config_manager.save_config(&new_config).await?;
                        
                        println!("✅ Configuration saved! Starting interactive mode...");
                        let mut interactive = InteractiveMode::new(new_config).await?;
                        interactive.run().await?;
                    }
                }
                Err(_) => {
                    println!("Welcome to Arien AI! 🚀");
                    println!("Let's get you set up...");
                    
                    info!("No configuration found, starting setup wizard");
                    let mut wizard = SetupWizard::new();
                    let config = wizard.run().await?;
                    config_manager.save_config(&config).await?;
                    
                    println!("✅ Configuration saved! Starting interactive mode...");
                    let mut interactive = InteractiveMode::new(config).await?;
                    interactive.run().await?;
                }
            }
        }
    }

    Ok(())
}

async fn ensure_configured(config_manager: &ConfigManager) -> Result<Config> {
    match config_manager.load_config().await {
        Ok(config) => Ok(config),
        Err(_) => {
            warn!("No configuration found, starting setup wizard");
            println!("Configuration not found. Let's set up Arien AI first...");
            
            let mut wizard = SetupWizard::new();
            let config = wizard.run().await?;
            config_manager.save_config(&config).await?;
            
            println!("✅ Configuration saved!");
            Ok(config)
        }
    }
}

fn init_logging(verbose: bool) -> Result<()> {
    let filter = if verbose {
        "arien=debug,info"
    } else {
        "arien=info,warn,error"
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| tracing_subscriber::EnvFilter::new(filter)),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}
