use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, info, warn};

use super::{ToolCall, ToolCallResponse, ToolContext, ToolRegistry, ToolResult};

/// Tool executor handles the execution of tool calls with safety checks and monitoring
#[derive(Clone)]
pub struct ToolExecutor {
    registry: Arc<ToolRegistry>,
    context: ToolContext,
}

impl ToolExecutor {
    /// Create a new tool executor
    pub fn new(registry: Arc<ToolRegistry>, context: ToolContext) -> Self {
        Self { registry, context }
    }

    /// Execute a single tool call
    pub async fn execute_tool_call(&self, call: ToolCall) -> Result<ToolCallResponse> {
        let start_time = Instant::now();
        
        debug!("Executing tool call: {} with id: {:?}", call.name, call.id);
        
        let result = self.registry
            .execute_tool(&call.name, call.parameters.clone(), &self.context)
            .await
            .unwrap_or_else(|error| {
                warn!("Tool execution error: {}", error);
                ToolResult::error(format!("Tool execution failed: {}", error))
            });

        let execution_time_ms = start_time.elapsed().as_millis() as u64;

        let response = ToolCallResponse {
            call,
            result,
            execution_time_ms,
        };

        info!("Tool call completed in {}ms", execution_time_ms);
        Ok(response)
    }

    /// Execute multiple tool calls in sequence
    pub async fn execute_tool_calls(&self, calls: Vec<ToolCall>) -> Result<Vec<ToolCallResponse>> {
        let mut responses = Vec::new();
        
        for call in calls {
            let response = self.execute_tool_call(call).await?;
            responses.push(response);
        }
        
        Ok(responses)
    }

    /// Execute multiple tool calls in parallel
    pub async fn execute_tool_calls_parallel(&self, calls: Vec<ToolCall>) -> Result<Vec<ToolCallResponse>> {
        let futures: Vec<_> = calls
            .into_iter()
            .map(|call| self.execute_tool_call(call))
            .collect();

        let results = futures::future::join_all(futures).await;
        
        let mut responses = Vec::new();
        for result in results {
            responses.push(result?);
        }
        
        Ok(responses)
    }

    /// Parse tool calls from LLM response
    pub fn parse_tool_calls(&self, llm_response: &str) -> Result<Vec<ToolCall>> {
        // Try to parse as JSON array first
        if let Ok(calls) = serde_json::from_str::<Vec<ToolCall>>(llm_response) {
            return Ok(calls);
        }

        // Try to parse as single tool call
        if let Ok(call) = serde_json::from_str::<ToolCall>(llm_response) {
            return Ok(vec![call]);
        }

        // Try to extract JSON from markdown code blocks
        let json_blocks = self.extract_json_blocks(llm_response);
        for block in json_blocks {
            if let Ok(calls) = serde_json::from_str::<Vec<ToolCall>>(&block) {
                return Ok(calls);
            }
            if let Ok(call) = serde_json::from_str::<ToolCall>(&block) {
                return Ok(vec![call]);
            }
        }

        anyhow::bail!("Failed to parse tool calls from LLM response")
    }

    /// Extract JSON blocks from markdown
    fn extract_json_blocks(&self, text: &str) -> Vec<String> {
        let mut blocks = Vec::new();
        let lines: Vec<&str> = text.lines().collect();
        let mut in_json_block = false;
        let mut current_block = String::new();

        for line in lines {
            if line.trim().starts_with("```json") || line.trim().starts_with("```") {
                if in_json_block {
                    // End of block
                    blocks.push(current_block.trim().to_string());
                    current_block.clear();
                    in_json_block = false;
                } else {
                    // Start of block
                    in_json_block = true;
                }
            } else if in_json_block {
                current_block.push_str(line);
                current_block.push('\n');
            }
        }

        // Handle case where block doesn't end with ```
        if in_json_block && !current_block.trim().is_empty() {
            blocks.push(current_block.trim().to_string());
        }

        blocks
    }

    /// Validate tool calls before execution
    pub fn validate_tool_calls(&self, calls: &[ToolCall]) -> Result<()> {
        for call in calls {
            // Check if tool exists
            if !self.registry.has_tool(&call.name) {
                anyhow::bail!("Unknown tool: {}", call.name);
            }

            // Validate parameters against schema
            if let Some(tool) = self.registry.get_tool(&call.name) {
                let schema = tool.schema();
                schema.validate(&call.parameters)
                    .with_context(|| format!("Parameter validation failed for tool: {}", call.name))?;
            }
        }
        
        Ok(())
    }

    /// Get execution statistics
    pub fn get_execution_stats(&self, responses: &[ToolCallResponse]) -> ExecutionStats {
        let total_calls = responses.len();
        let successful_calls = responses.iter().filter(|r| r.result.success).count();
        let failed_calls = total_calls - successful_calls;
        let total_time_ms = responses.iter().map(|r| r.execution_time_ms).sum();
        let average_time_ms = if total_calls > 0 {
            total_time_ms / total_calls as u64
        } else {
            0
        };

        ExecutionStats {
            total_calls,
            successful_calls,
            failed_calls,
            total_time_ms,
            average_time_ms,
        }
    }

    /// Update the execution context
    pub fn with_context(mut self, context: ToolContext) -> Self {
        self.context = context;
        self
    }

    /// Get the current context
    pub fn context(&self) -> &ToolContext {
        &self.context
    }

    /// Get the tool registry
    pub fn registry(&self) -> &ToolRegistry {
        &self.registry
    }
}

/// Execution statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStats {
    pub total_calls: usize,
    pub successful_calls: usize,
    pub failed_calls: usize,
    pub total_time_ms: u64,
    pub average_time_ms: u64,
}

impl ExecutionStats {
    /// Get success rate as percentage
    pub fn success_rate(&self) -> f64 {
        if self.total_calls == 0 {
            0.0
        } else {
            (self.successful_calls as f64 / self.total_calls as f64) * 100.0
        }
    }

    /// Format stats for display
    pub fn format_stats(&self) -> String {
        format!(
            "Execution Stats:\n  Total calls: {}\n  Successful: {}\n  Failed: {}\n  Success rate: {:.1}%\n  Total time: {}ms\n  Average time: {}ms",
            self.total_calls,
            self.successful_calls,
            self.failed_calls,
            self.success_rate(),
            self.total_time_ms,
            self.average_time_ms
        )
    }
}

/// Tool execution mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ExecutionMode {
    /// Execute tools sequentially
    Sequential,
    /// Execute tools in parallel where possible
    Parallel,
    /// Execute with user confirmation for each tool
    Interactive,
}

/// Advanced tool executor with different execution modes
#[derive(Debug, Clone)]
pub struct AdvancedToolExecutor {
    executor: ToolExecutor,
    mode: ExecutionMode,
}

impl AdvancedToolExecutor {
    pub fn new(registry: Arc<ToolRegistry>, context: ToolContext, mode: ExecutionMode) -> Self {
        Self {
            executor: ToolExecutor::new(registry, context),
            mode,
        }
    }

    pub async fn execute(&self, calls: Vec<ToolCall>) -> Result<Vec<ToolCallResponse>> {
        match self.mode {
            ExecutionMode::Sequential => self.executor.execute_tool_calls(calls).await,
            ExecutionMode::Parallel => self.executor.execute_tool_calls_parallel(calls).await,
            ExecutionMode::Interactive => self.execute_interactive(calls).await,
        }
    }

    async fn execute_interactive(&self, calls: Vec<ToolCall>) -> Result<Vec<ToolCallResponse>> {
        let mut responses = Vec::new();
        
        for call in calls {
            // Show tool call details to user
            println!("About to execute tool: {}", call.name);
            println!("Parameters: {}", serde_json::to_string_pretty(&call.parameters)?);
            
            // Get user confirmation
            print!("Proceed? (y/n): ");
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if input.trim().to_lowercase().starts_with('y') {
                let response = self.executor.execute_tool_call(call).await?;
                responses.push(response);
            } else {
                // Create a cancelled response
                let cancelled_result = ToolResult::error("Execution cancelled by user".to_string());
                let response = ToolCallResponse {
                    call,
                    result: cancelled_result,
                    execution_time_ms: 0,
                };
                responses.push(response);
            }
        }
        
        Ok(responses)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tools::registry::ToolRegistryBuilder;

    #[tokio::test]
    async fn test_tool_executor() -> Result<()> {
        let registry = Arc::new(
            ToolRegistryBuilder::new()
                .with_default_tools()?
                .build()
        );
        
        let context = ToolContext::default();
        let executor = ToolExecutor::new(registry, context);

        let call = ToolCall {
            name: "read_file".to_string(),
            parameters: serde_json::json!({
                "path": "Cargo.toml"
            }),
            id: Some("test-1".to_string()),
        };

        let response = executor.execute_tool_call(call).await?;
        assert_eq!(response.call.name, "read_file");
        
        Ok(())
    }

    #[test]
    fn test_json_extraction() {
        let executor = ToolExecutor::new(
            Arc::new(crate::tools::registry::ToolRegistry::new()),
            ToolContext::default()
        );

        let text = r#"
Here's the tool call:

```json
{
    "name": "test_tool",
    "parameters": {"key": "value"}
}
```

That should work.
        "#;

        let blocks = executor.extract_json_blocks(text);
        assert_eq!(blocks.len(), 1);
        assert!(blocks[0].contains("test_tool"));
    }

    #[test]
    fn test_execution_stats() {
        let stats = ExecutionStats {
            total_calls: 10,
            successful_calls: 8,
            failed_calls: 2,
            total_time_ms: 1000,
            average_time_ms: 100,
        };

        assert_eq!(stats.success_rate(), 80.0);
        assert!(stats.format_stats().contains("80.0%"));
    }
}
