use anyhow::{Context, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use schemars::JsonSchema;
use std::collections::HashMap;
use std::process::Stdio;
use std::time::Duration;
use tokio::process::Command;
use tokio::time::timeout;
use tracing::{debug, warn};

use super::{Tool, ToolCategory, ToolContext, ToolResult, ToolSchema};
use super::schema::{validation, SchemaBuilder};

/// Execute shell command tool
pub struct ExecuteCommandToolImpl;

impl ExecuteCommandToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct ExecuteCommandParams {
    /// Command to execute
    command: String,
    /// Working directory (optional)
    working_dir: Option<String>,
    /// Environment variables (optional)
    env: Option<HashMap<String, String>>,
    /// Timeout in seconds (optional, default: 300)
    timeout_seconds: Option<u64>,
    /// Capture stdout (default: true)
    capture_stdout: Option<bool>,
    /// Capture stderr (default: true)
    capture_stderr: Option<bool>,
}

#[async_trait]
impl Tool for ExecuteCommandToolImpl {
    fn name(&self) -> &str {
        "execute_command"
    }

    fn description(&self) -> &str {
        "Execute a shell command and return the output. Use with caution for system commands."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .requires_confirmation(true)
            .with_example(serde_json::json!({
                "command": "ls -la"
            }))
            .with_example(serde_json::json!({
                "command": "git status",
                "working_dir": "./project"
            }))
            .with_example(serde_json::json!({
                "command": "echo $MY_VAR",
                "env": {"MY_VAR": "hello world"}
            }))
            .build::<ExecuteCommandParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Shell
    }

    fn requires_confirmation(&self) -> bool {
        true
    }

    fn is_safe(&self) -> bool {
        false
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: ExecuteCommandParams = serde_json::from_value(params)
            .context("Failed to parse execute_command parameters")?;

        // Validate command safety
        validation::validate_safe_command(&params.command)?;

        debug!("Executing command: {}", params.command);

        let working_dir = if let Some(ref dir) = params.working_dir {
            validation::validate_safe_path(dir)?;
            if std::path::Path::new(dir).is_absolute() {
                dir.clone().into()
            } else {
                context.working_dir.join(dir)
            }
        } else {
            context.working_dir.clone()
        };

        // Parse command into parts
        let command_parts = shell_words::split(&params.command)
            .with_context(|| format!("Failed to parse command: {}", params.command))?;

        if command_parts.is_empty() {
            return Ok(ToolResult::error("Empty command".to_string()));
        }

        let (program, args) = command_parts.split_first().unwrap();

        let mut cmd = Command::new(program);
        cmd.args(args);
        cmd.current_dir(&working_dir);

        // Set environment variables
        if let Some(env_vars) = &params.env {
            for (key, value) in env_vars {
                cmd.env(key, value);
            }
        }

        // Configure stdio
        if params.capture_stdout.unwrap_or(true) {
            cmd.stdout(Stdio::piped());
        }
        if params.capture_stderr.unwrap_or(true) {
            cmd.stderr(Stdio::piped());
        }

        let timeout_duration = Duration::from_secs(
            params.timeout_seconds.unwrap_or(context.timeout_seconds)
        );

        let start_time = std::time::Instant::now();
        
        let result = timeout(timeout_duration, cmd.output()).await;

        let execution_time = start_time.elapsed();

        match result {
            Ok(Ok(output)) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let stderr = String::from_utf8_lossy(&output.stderr);
                let exit_code = output.status.code().unwrap_or(-1);

                let success = output.status.success();
                let output_text = if success {
                    stdout.to_string()
                } else {
                    format!("Command failed with exit code {}\nStdout: {}\nStderr: {}", 
                           exit_code, stdout, stderr)
                };

                let metadata = serde_json::json!({
                    "command": params.command,
                    "working_dir": working_dir.display().to_string(),
                    "exit_code": exit_code,
                    "execution_time_ms": execution_time.as_millis(),
                    "stdout": stdout.to_string(),
                    "stderr": stderr.to_string()
                });

                if success {
                    Ok(ToolResult::success(output_text)
                        .with_metadata("execution_info".to_string(), metadata))
                } else {
                    Ok(ToolResult::error(output_text)
                        .with_metadata("execution_info".to_string(), metadata))
                }
            }
            Ok(Err(error)) => {
                warn!("Command execution failed: {}", error);
                Ok(ToolResult::error(format!("Failed to execute command: {}", error)))
            }
            Err(_) => {
                warn!("Command timed out after {:?}", timeout_duration);
                Ok(ToolResult::error(format!(
                    "Command timed out after {} seconds", 
                    timeout_duration.as_secs()
                )))
            }
        }
    }
}

/// Which command tool - find executable location
pub struct WhichCommandToolImpl;

impl WhichCommandToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct WhichCommandParams {
    /// Name of the command to locate
    command: String,
}

#[async_trait]
impl Tool for WhichCommandToolImpl {
    fn name(&self) -> &str {
        "which_command"
    }

    fn description(&self) -> &str {
        "Find the location of an executable command in the system PATH."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "command": "git"
            }))
            .with_example(serde_json::json!({
                "command": "python"
            }))
            .build::<WhichCommandParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Shell
    }

    async fn execute(&self, params: serde_json::Value, _context: &ToolContext) -> Result<ToolResult> {
        let params: WhichCommandParams = serde_json::from_value(params)
            .context("Failed to parse which_command parameters")?;

        debug!("Looking for command: {}", params.command);

        match which::which(&params.command) {
            Ok(path) => {
                let metadata = serde_json::json!({
                    "command": params.command,
                    "path": path.display().to_string(),
                    "exists": true
                });

                Ok(ToolResult::success(format!(
                    "Command '{}' found at: {}",
                    params.command,
                    path.display()
                )).with_metadata("which_info".to_string(), metadata))
            }
            Err(_) => {
                let metadata = serde_json::json!({
                    "command": params.command,
                    "exists": false
                });

                Ok(ToolResult::error(format!(
                    "Command '{}' not found in PATH",
                    params.command
                )).with_metadata("which_info".to_string(), metadata))
            }
        }
    }
}

/// Get environment variable tool
pub struct GetEnvToolImpl;

impl GetEnvToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct GetEnvParams {
    /// Name of the environment variable
    name: String,
    /// Default value if variable is not set
    default: Option<String>,
}

#[async_trait]
impl Tool for GetEnvToolImpl {
    fn name(&self) -> &str {
        "get_env"
    }

    fn description(&self) -> &str {
        "Get the value of an environment variable."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "name": "PATH"
            }))
            .with_example(serde_json::json!({
                "name": "MY_VAR",
                "default": "default_value"
            }))
            .build::<GetEnvParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Shell
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: GetEnvParams = serde_json::from_value(params)
            .context("Failed to parse get_env parameters")?;

        debug!("Getting environment variable: {}", params.name);

        let value = context.env_vars.get(&params.name)
            .or_else(|| std::env::var(&params.name).ok().as_ref())
            .cloned()
            .or(params.default);

        match value {
            Some(val) => {
                let metadata = serde_json::json!({
                    "variable_name": params.name,
                    "value": val,
                    "exists": true
                });

                Ok(ToolResult::success(val)
                    .with_metadata("env_info".to_string(), metadata))
            }
            None => {
                let metadata = serde_json::json!({
                    "variable_name": params.name,
                    "exists": false
                });

                Ok(ToolResult::error(format!(
                    "Environment variable '{}' not found",
                    params.name
                )).with_metadata("env_info".to_string(), metadata))
            }
        }
    }
}

/// Change working directory tool
pub struct ChangeDirectoryToolImpl;

impl ChangeDirectoryToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct ChangeDirectoryParams {
    /// Path to change to
    path: String,
}

#[async_trait]
impl Tool for ChangeDirectoryToolImpl {
    fn name(&self) -> &str {
        "change_directory"
    }

    fn description(&self) -> &str {
        "Change the current working directory for subsequent operations."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "path": "../parent"
            }))
            .with_example(serde_json::json!({
                "path": "/absolute/path"
            }))
            .build::<ChangeDirectoryParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Shell
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: ChangeDirectoryParams = serde_json::from_value(params)
            .context("Failed to parse change_directory parameters")?;

        validation::validate_safe_path(&params.path)?;

        let new_dir = if std::path::Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Changing directory to: {}", new_dir.display());

        let canonical_path = tokio::fs::canonicalize(&new_dir).await
            .with_context(|| format!("Failed to resolve path: {}", new_dir.display()))?;

        if !canonical_path.is_dir() {
            return Ok(ToolResult::error(format!(
                "Path is not a directory: {}",
                canonical_path.display()
            )));
        }

        let metadata = serde_json::json!({
            "old_directory": context.working_dir.display().to_string(),
            "new_directory": canonical_path.display().to_string()
        });

        Ok(ToolResult::success(format!(
            "Changed directory to: {}",
            canonical_path.display()
        )).with_metadata("cd_info".to_string(), metadata))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_which_command() -> Result<()> {
        let tool = WhichCommandToolImpl::new();
        let context = ToolContext::default();
        
        // Test with a command that should exist on most systems
        let params = serde_json::json!({
            "command": if cfg!(windows) { "cmd" } else { "sh" }
        });
        
        let result = tool.execute(params, &context).await?;
        assert!(result.success);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_get_env() -> Result<()> {
        let tool = GetEnvToolImpl::new();
        let context = ToolContext::default();
        
        // Test with PATH which should exist
        let params = serde_json::json!({
            "name": "PATH"
        });
        
        let result = tool.execute(params, &context).await?;
        assert!(result.success);
        assert!(!result.output.is_empty());
        
        // Test with non-existent variable
        let params = serde_json::json!({
            "name": "NONEXISTENT_VAR_12345"
        });
        
        let result = tool.execute(params, &context).await?;
        assert!(!result.success);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_change_directory() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let tool = ChangeDirectoryToolImpl::new();
        let context = ToolContext {
            working_dir: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        
        let params = serde_json::json!({
            "path": "."
        });
        
        let result = tool.execute(params, &context).await?;
        assert!(result.success);
        
        Ok(())
    }
}
