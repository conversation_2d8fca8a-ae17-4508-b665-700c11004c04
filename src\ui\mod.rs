use anyhow::Result;

pub mod components;
pub mod interactive;
pub mod setup_wizard;

pub use interactive::InteractiveMode;
pub use setup_wizard::SetupWizard;

/// Common UI utilities and helpers
pub mod utils {
    use console::{style, Style, Term};
    use std::io::Write;

    /// Print a styled header
    pub fn print_header(text: &str) {
        let term = Term::stdout();
        let _ = term.write_line(&format!("\n{}", style(text).bold().cyan()));
    }

    /// Print a success message
    pub fn print_success(text: &str) {
        let term = Term::stdout();
        let _ = term.write_line(&format!("{} {}", style("✅").green(), text));
    }

    /// Print an error message
    pub fn print_error(text: &str) {
        let term = Term::stderr();
        let _ = term.write_line(&format!("{} {}", style("❌").red(), text));
    }

    /// Print a warning message
    pub fn print_warning(text: &str) {
        let term = Term::stdout();
        let _ = term.write_line(&format!("{} {}", style("⚠️").yellow(), text));
    }

    /// Print an info message
    pub fn print_info(text: &str) {
        let term = Term::stdout();
        let _ = term.write_line(&format!("{} {}", style("ℹ️").blue(), text));
    }

    /// Print a spinner with text
    pub fn print_spinner(text: &str) {
        let term = Term::stdout();
        let _ = term.write_line(&format!("{} {}", style("⏳").cyan(), text));
    }

    /// Clear the current line
    pub fn clear_line() {
        let term = Term::stdout();
        let _ = term.clear_line();
    }

    /// Move cursor up
    pub fn move_cursor_up(lines: usize) {
        let term = Term::stdout();
        let _ = term.move_cursor_up(lines);
    }

    /// Get user input with prompt
    pub fn get_input(prompt: &str) -> anyhow::Result<String> {
        let mut term = Term::stdout();
        term.write_all(format!("{} ", style(prompt).bold()).to_string().as_bytes())?;
        term.flush()?;
        
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        Ok(input.trim().to_string())
    }

    /// Get user confirmation (y/n)
    pub fn get_confirmation(prompt: &str) -> anyhow::Result<bool> {
        loop {
            let input = get_input(&format!("{} (y/n):", prompt))?;
            match input.to_lowercase().as_str() {
                "y" | "yes" => return Ok(true),
                "n" | "no" => return Ok(false),
                _ => print_warning("Please enter 'y' or 'n'"),
            }
        }
    }

    /// Get user choice from a list of options
    pub fn get_choice(prompt: &str, options: &[&str]) -> anyhow::Result<usize> {
        println!("\n{}", style(prompt).bold());
        for (i, option) in options.iter().enumerate() {
            println!("  {}. {}", i + 1, option);
        }
        
        loop {
            let input = get_input("Enter your choice:")?;
            if let Ok(choice) = input.parse::<usize>() {
                if choice > 0 && choice <= options.len() {
                    return Ok(choice - 1);
                }
            }
            print_warning(&format!("Please enter a number between 1 and {}", options.len()));
        }
    }

    /// Display a progress bar
    pub struct ProgressBar {
        total: usize,
        current: usize,
        width: usize,
    }

    impl ProgressBar {
        pub fn new(total: usize) -> Self {
            Self {
                total,
                current: 0,
                width: 50,
            }
        }

        pub fn update(&mut self, current: usize) {
            self.current = current;
            self.display();
        }

        pub fn increment(&mut self) {
            self.current += 1;
            self.display();
        }

        pub fn finish(&mut self) {
            self.current = self.total;
            self.display();
            println!();
        }

        fn display(&self) {
            let percentage = if self.total > 0 {
                (self.current as f64 / self.total as f64 * 100.0) as usize
            } else {
                100
            };

            let filled = (self.current as f64 / self.total as f64 * self.width as f64) as usize;
            let empty = self.width - filled;

            let bar = format!(
                "[{}{}] {}% ({}/{})",
                "█".repeat(filled),
                "░".repeat(empty),
                percentage,
                self.current,
                self.total
            );

            print!("\r{}", bar);
            std::io::stdout().flush().unwrap_or(());
        }
    }

    /// Format file size in human readable format
    pub fn format_file_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", size as u64, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Format duration in human readable format
    pub fn format_duration(duration: std::time::Duration) -> String {
        let total_seconds = duration.as_secs();
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        let seconds = total_seconds % 60;
        let millis = duration.subsec_millis();

        if hours > 0 {
            format!("{}h {}m {}s", hours, minutes, seconds)
        } else if minutes > 0 {
            format!("{}m {}s", minutes, seconds)
        } else if seconds > 0 {
            format!("{}.{}s", seconds, millis / 100)
        } else {
            format!("{}ms", millis)
        }
    }

    /// Create a styled table
    pub struct Table {
        headers: Vec<String>,
        rows: Vec<Vec<String>>,
        max_widths: Vec<usize>,
    }

    impl Table {
        pub fn new(headers: Vec<String>) -> Self {
            let max_widths = headers.iter().map(|h| h.len()).collect();
            Self {
                headers,
                rows: Vec::new(),
                max_widths,
            }
        }

        pub fn add_row(&mut self, row: Vec<String>) {
            for (i, cell) in row.iter().enumerate() {
                if i < self.max_widths.len() {
                    self.max_widths[i] = self.max_widths[i].max(cell.len());
                }
            }
            self.rows.push(row);
        }

        pub fn display(&self) {
            // Print header
            self.print_separator();
            self.print_row(&self.headers, true);
            self.print_separator();

            // Print rows
            for row in &self.rows {
                self.print_row(row, false);
            }
            self.print_separator();
        }

        fn print_separator(&self) {
            let separator: String = self.max_widths
                .iter()
                .map(|&width| "-".repeat(width + 2))
                .collect::<Vec<_>>()
                .join("+");
            println!("+{}+", separator);
        }

        fn print_row(&self, row: &[String], is_header: bool) {
            let formatted_cells: Vec<String> = row
                .iter()
                .enumerate()
                .map(|(i, cell)| {
                    let width = self.max_widths.get(i).unwrap_or(&0);
                    let padded = format!(" {:width$} ", cell, width = width);
                    if is_header {
                        style(padded).bold().to_string()
                    } else {
                        padded
                    }
                })
                .collect();

            println!("|{}|", formatted_cells.join("|"));
        }
    }
}

#[cfg(test)]
mod tests {
    use super::utils::*;

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }

    #[test]
    fn test_format_duration() {
        use std::time::Duration;
        
        assert_eq!(format_duration(Duration::from_millis(500)), "500ms");
        assert_eq!(format_duration(Duration::from_secs(1)), "1.0s");
        assert_eq!(format_duration(Duration::from_secs(65)), "1m 5s");
        assert_eq!(format_duration(Duration::from_secs(3665)), "1h 1m 5s");
    }

    #[test]
    fn test_progress_bar() {
        let mut progress = ProgressBar::new(100);
        progress.update(50);
        assert_eq!(progress.current, 50);
        
        progress.increment();
        assert_eq!(progress.current, 51);
    }

    #[test]
    fn test_table() {
        let mut table = Table::new(vec!["Name".to_string(), "Age".to_string()]);
        table.add_row(vec!["Alice".to_string(), "30".to_string()]);
        table.add_row(vec!["Bob".to_string(), "25".to_string()]);
        
        assert_eq!(table.rows.len(), 2);
        assert_eq!(table.max_widths[0], 5); // "Alice".len()
    }
}
